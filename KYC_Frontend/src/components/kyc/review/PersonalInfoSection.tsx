import React from "react";
import { Input } from "@/components/ui/input";
import { PersonalInfo } from "@/redux/slices/kycSlice";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PersonalInfoSectionProps {
  personalInfo: PersonalInfo;
  editingPersonal: boolean;
  setEditingPersonal: (editing: boolean) => void;
  handlePersonalChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange?: (name: string, value: string) => void;
  handleCheckboxChange?: (name: string, checked: boolean) => void;
  handleNumberChange?: (name: string, value: number) => void;
  savePersonalInfo: () => void;
}

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  personalInfo,
  editingPersonal,
  setEditingPersonal,
  handlePersonalChange,
  handleSelectChange = () => {},
  handleCheckboxChange = () => {},
  handleNumberChange = () => {},
  savePersonalInfo,
}) => {
  return (
    <div className="border rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          Personal Information
        </h3>
        {!editingPersonal ? (
          <Button
            onClick={() => setEditingPersonal(true)}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            <Edit className="w-4 h-4 mr-1" />
            Edit Personal info
          </Button>
        ) : (
          <Button
            onClick={savePersonalInfo}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            Save Changes
          </Button>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">First name</p>
          {editingPersonal ? (
            <Input
              name="firstName"
              value={personalInfo.firstName}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.firstName}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Last name</p>
          {editingPersonal ? (
            <Input
              name="lastName"
              value={personalInfo.lastName}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.lastName}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Email Address</p>
          {editingPersonal ? (
            <Input
              name="email"
              value={personalInfo.email}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.email}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Mobile number</p>
          {editingPersonal ? (
            <Input
              name="mobile"
              value={personalInfo.mobile}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.mobile}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Date of Birth</p>
          {editingPersonal ? (
            <Input
              name="dob"
              value={personalInfo.dob}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
              placeholder="DD/MM/YYYY"
            />
          ) : (
            <p>{personalInfo.dob || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Gender</p>
          {editingPersonal ? (
            <Select
              value={personalInfo.gender}
              onValueChange={(value) => handleSelectChange("gender", value)}
            >
              <SelectTrigger className="mt-1 bg-[#F8F9FB]">
                <SelectValue placeholder="Select Gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Male">Male</SelectItem>
                <SelectItem value="Female">Female</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <p>
              {personalInfo.gender
                ? personalInfo.gender.charAt(0).toUpperCase() +
                  personalInfo.gender.slice(1)
                : "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Father name</p>
          {editingPersonal ? (
            <Input
              name="fatherName"
              value={personalInfo.fatherName}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.fatherName || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Mother name</p>
          {editingPersonal ? (
            <Input
              name="motherName"
              value={personalInfo.motherName}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.motherName || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Marital Status</p>
          {editingPersonal ? (
            <div className="flex items-center gap-2 mt-1">
              <Checkbox
                id="isMarried"
                checked={personalInfo.isMarried}
                onCheckedChange={(checked) =>
                  handleCheckboxChange("isMarried", checked as boolean)
                }
              />
              <Label htmlFor="isMarried">Married</Label>
            </div>
          ) : (
            <p>{personalInfo.isMarried ? "Married" : "Single"}</p>
          )}
        </div>
        {(personalInfo.isMarried || personalInfo.spouseName) && (
          <div>
            <p className="text-sm text-gray-500">Spouse name</p>
            {editingPersonal ? (
              <Input
                name="spouseName"
                value={personalInfo.spouseName || ""}
                onChange={handlePersonalChange}
                className="mt-1 bg-[#F8F9FB]"
              />
            ) : (
              <p>{personalInfo.spouseName || "N/A"}</p>
            )}
          </div>
        )}
        <div>
          <p className="text-sm text-gray-500">Number of Dependents</p>
          {editingPersonal ? (
            <div className="flex items-center mt-1">
              <button
                type="button"
                className="border rounded-l px-3 py-1"
                onClick={() =>
                  handleNumberChange(
                    "dependents",
                    Math.max(0, personalInfo.dependents - 1)
                  )
                }
              >
                &lt;
              </button>
              <input
                type="number"
                value={personalInfo.dependents}
                onChange={(e) =>
                  handleNumberChange(
                    "dependents",
                    parseInt(e.target.value) || 0
                  )
                }
                className="border-t border-b text-center w-12 py-1"
                min="0"
              />
              <button
                type="button"
                className="border rounded-r px-3 py-1"
                onClick={() =>
                  handleNumberChange("dependents", personalInfo.dependents + 1)
                }
              >
                &gt;
              </button>
            </div>
          ) : (
            <p>{personalInfo.dependents}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Guardian Name</p>
          {editingPersonal ? (
            <Input
              name="guardianName"
              value={personalInfo.guardianName || ""}
              onChange={handlePersonalChange}
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{personalInfo.guardianName || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Guardian Relation</p>
          {editingPersonal ? (
            <Select
              value={personalInfo.guardianRelation}
              onValueChange={(value) =>
                handleSelectChange("guardianRelation", value)
              }
            >
              <SelectTrigger className="mt-1 bg-[#F8F9FB]">
                <SelectValue placeholder="Relation with guardian" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Parent">Parent</SelectItem>
                <SelectItem value="Sibling">Sibling</SelectItem>
                <SelectItem value="Relative">Relative</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <p>
              {personalInfo.guardianRelation
                ? personalInfo.guardianRelation.charAt(0).toUpperCase() +
                  personalInfo.guardianRelation.slice(1)
                : "N/A"}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
