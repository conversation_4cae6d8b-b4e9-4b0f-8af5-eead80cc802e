import React from "react";
import { Input } from "@/components/ui/input";

interface AddressFormProps {
  addressType: "temporary" | "permanent";
  addressData: {
    house_no: string;
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  errors: {
    house_no: string;
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  handleChange: (
    addressType: "temporary" | "permanent",
    field: string,
    value: string
  ) => void;
  title?: string;
}

const AddressForm: React.FC<AddressFormProps> = ({
  addressType,
  addressData,
  errors,
  handleChange,
  title,
}) => {
  return (
    <div className="space-y-4">
      {title && <h3 className="text-lg font-medium text-gray-700">{title}</h3>}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Input
            value={addressData.house_no}
            onChange={(e) =>
              handleChange(addressType, "house_no", e.target.value)
            }
            placeholder="House No."
            className={`h-12 ${errors.house_no ? "border-red-500" : ""}`}
          />
          {errors.house_no && (
            <p className="mt-1 text-xs text-red-500">{errors.house_no}</p>
          )}
        </div>

        <div>
          <Input
            value={addressData.street}
            onChange={(e) =>
              handleChange(addressType, "street", e.target.value)
            }
            placeholder="Street, Landmark"
            className={`h-12 ${errors.street ? "border-red-500" : ""}`}
          />
          {errors.street && (
            <p className="mt-1 text-xs text-red-500">{errors.street}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Input
            value={addressData.city}
            onChange={(e) => handleChange(addressType, "city", e.target.value)}
            placeholder="City / Town"
            className={`h-12 ${errors.city ? "border-red-500" : ""}`}
          />
          {errors.city && (
            <p className="mt-1 text-xs text-red-500">{errors.city}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Input
              value={addressData.state}
              onChange={(e) =>
                handleChange(addressType, "state", e.target.value)
              }
              placeholder="State / UT"
              className={`h-12 ${errors.state ? "border-red-500" : ""}`}
            />
            {errors.state && (
              <p className="mt-1 text-xs text-red-500">{errors.state}</p>
            )}
          </div>

          <div>
            <Input
              value={addressData.pincode}
              onChange={(e) =>
                handleChange(addressType, "pincode", e.target.value)
              }
              placeholder="Pincode"
              type="text"
              maxLength={6}
              className={`h-12 ${errors.pincode ? "border-red-500" : ""}`}
            />
            {errors.pincode && (
              <p className="mt-1 text-xs text-red-500">{errors.pincode}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressForm;
